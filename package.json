{"name": "my-expo-app", "version": "1.0.0", "scripts": {"android": "expo start --android", "ios": "expo start --ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "expo": "~53.0.19", "expo-status-bar": "~2.2.3", "nativewind": "latest", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-ssh-sftp": "^1.0.3", "react-native-syntax-highlighter": "^2.1.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "jest": "^30.0.4", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "react-test-renderer": "^19.1.0", "tailwindcss": "^3.4.0", "ts-jest": "^29.4.0", "typescript": "~5.8.3"}, "main": "node_modules/expo/AppEntry.js", "private": true}