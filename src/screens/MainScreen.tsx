import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Alert,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import {
  SSHConfig,
  ConnectionStatus,
  FileSystemItem,
  CodeSelection,
  AIAssistantType,
  AIAssistantConfig,
  AIAssistantStatus,
  AIAgent,
} from '../types';
import { SSHService } from '../services/SSHService';
import { AgentFactory } from '../services/agents/AgentFactory';
import { FileSystemBrowser } from '../components/FileSystemBrowser';
import { CodeViewer } from '../components/CodeViewer';
import { AIAssistantChat } from '../components/AIAssistantChat';
import { SSHConnectionModal } from '../components/SSHConnectionModal';

export const MainScreen: React.FC = () => {
  // SSH连接状态
  const [sshService] = useState(() => new SSHService());
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(
    ConnectionStatus.DISCONNECTED
  );
  const [showConnectionModal, setShowConnectionModal] = useState(false);

  // 文件系统状态
  const [currentPath, setCurrentPath] = useState('/home');
  const [selectedFile, setSelectedFile] = useState<FileSystemItem | null>(null);
  const [showCodeViewer, setShowCodeViewer] = useState(false);

  // AI助手状态
  const [aiAgent, setAiAgent] = useState<AIAgent | null>(null);
  const [aiStatus, setAiStatus] = useState<AIAssistantStatus>(AIAssistantStatus.IDLE);

  // 代码选择状态
  const [codeSelection, setCodeSelection] = useState<CodeSelection | null>(null);
  const [selectedFilePath, setSelectedFilePath] = useState<string | null>(null);

  // 界面状态
  const [activeTab, setActiveTab] = useState<'files' | 'chat'>('files');

  useEffect(() => {
    // 监听SSH连接状态变化
    sshService.onStatusChange((status) => {
      setConnectionStatus(status);
      if (status === ConnectionStatus.DISCONNECTED && aiAgent) {
        // SSH断开时停止AI助手
        aiAgent.stop();
        setAiAgent(null);
        setAiStatus(AIAssistantStatus.IDLE);
      }
    });
  }, [sshService, aiAgent]);

  const handleConnect = async (config: SSHConfig) => {
    try {
      await sshService.connect(config);
      setShowConnectionModal(false);
      Alert.alert('成功', 'SSH连接已建立');
    } catch (error) {
      Alert.alert('连接失败', `无法连接到服务器: ${error}`);
    }
  };

  const handleDisconnect = async () => {
    try {
      if (aiAgent) {
        await aiAgent.stop();
        setAiAgent(null);
        setAiStatus(AIAssistantStatus.IDLE);
      }
      await sshService.disconnect();
      Alert.alert('已断开', 'SSH连接已断开');
    } catch (error) {
      Alert.alert('错误', `断开连接时出错: ${error}`);
    }
  };

  const handleFileSelect = (file: FileSystemItem) => {
    setSelectedFile(file);
    setShowCodeViewer(true);
  };

  const handleCodeSelect = (selection: CodeSelection) => {
    setCodeSelection(selection);
    setSelectedFilePath(selectedFile?.path || null);
    setShowCodeViewer(false);
    setActiveTab('chat');
  };

  const handleClearCodeContext = () => {
    setCodeSelection(null);
    setSelectedFilePath(null);
  };

  const startAIAssistant = async () => {
    if (connectionStatus !== ConnectionStatus.CONNECTED) {
      Alert.alert('错误', '请先建立SSH连接');
      return;
    }

    try {
      const agent = AgentFactory.createAgent(AIAssistantType.CLAUDE_CODE, sshService);
      
      agent.onStatusChange((status) => {
        setAiStatus(status);
      });

      const config: AIAssistantConfig = {
        type: AIAssistantType.CLAUDE_CODE,
        projectPath: currentPath,
      };

      await agent.start(config);
      setAiAgent(agent);
      setActiveTab('chat');
      
      Alert.alert('成功', 'Claude Code已启动');
    } catch (error) {
      Alert.alert('启动失败', `无法启动AI助手: ${error}`);
    }
  };

  const stopAIAssistant = async () => {
    if (aiAgent) {
      try {
        await aiAgent.stop();
        setAiAgent(null);
        setAiStatus(AIAssistantStatus.IDLE);
        Alert.alert('已停止', 'AI助手已停止');
      } catch (error) {
        Alert.alert('错误', `停止AI助手时出错: ${error}`);
      }
    }
  };

  const renderConnectionStatus = () => {
    const getStatusColor = () => {
      switch (connectionStatus) {
        case ConnectionStatus.CONNECTED:
          return '#4CAF50';
        case ConnectionStatus.CONNECTING:
          return '#FF9800';
        case ConnectionStatus.ERROR:
          return '#F44336';
        default:
          return '#9E9E9E';
      }
    };

    const getStatusText = () => {
      switch (connectionStatus) {
        case ConnectionStatus.CONNECTED:
          return '已连接';
        case ConnectionStatus.CONNECTING:
          return '连接中...';
        case ConnectionStatus.ERROR:
          return '连接错误';
        default:
          return '未连接';
      }
    };

    return (
      <View className="flex-row items-center justify-between mb-2">
        <View className="flex-row items-center">
          <View
            className="w-2 h-2 rounded-full mr-2"
            style={{ backgroundColor: getStatusColor() }}
          />
          <Text className="text-sm text-gray-600">SSH: {getStatusText()}</Text>
        </View>

        <View className="flex-row">
          {connectionStatus === ConnectionStatus.CONNECTED ? (
            <TouchableOpacity
              className="bg-red-500 px-3 py-1.5 rounded"
              onPress={handleDisconnect}
            >
              <Text className="text-white text-sm font-medium">断开</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              className="bg-blue-500 px-3 py-1.5 rounded"
              onPress={() => setShowConnectionModal(true)}
            >
              <Text className="text-white text-sm font-medium">连接</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderAIStatus = () => {
    if (connectionStatus !== ConnectionStatus.CONNECTED) {
      return null;
    }

    const getAIStatusColor = () => {
      switch (aiStatus) {
        case AIAssistantStatus.READY:
          return '#4CAF50';
        case AIAssistantStatus.PROCESSING:
          return '#FF9800';
        case AIAssistantStatus.ERROR:
          return '#F44336';
        default:
          return '#9E9E9E';
      }
    };

    const getAIStatusText = () => {
      switch (aiStatus) {
        case AIAssistantStatus.READY:
          return '就绪';
        case AIAssistantStatus.STARTING:
          return '启动中...';
        case AIAssistantStatus.PROCESSING:
          return '处理中...';
        case AIAssistantStatus.ERROR:
          return '错误';
        default:
          return '未启动';
      }
    };

    return (
      <View className="flex-row items-center justify-between mb-2">
        <View className="flex-row items-center">
          <View
            className="w-2 h-2 rounded-full mr-2"
            style={{ backgroundColor: getAIStatusColor() }}
          />
          <Text className="text-sm text-gray-600">AI助手: {getAIStatusText()}</Text>
        </View>

        <View className="flex-row">
          {aiAgent ? (
            <TouchableOpacity
              className="bg-red-500 px-3 py-1.5 rounded"
              onPress={stopAIAssistant}
            >
              <Text className="text-white text-sm font-medium">停止</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              className="bg-blue-500 px-3 py-1.5 rounded"
              onPress={startAIAssistant}
            >
              <Text className="text-white text-sm font-medium">启动</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderTabBar = () => (
    <View className="flex-row bg-white border-b border-gray-200">
      <TouchableOpacity
        className={`flex-1 py-3 items-center ${
          activeTab === 'files' ? 'border-b-2 border-blue-500' : ''
        }`}
        onPress={() => setActiveTab('files')}
      >
        <Text className={`text-base ${
          activeTab === 'files' ? 'text-blue-500 font-medium' : 'text-gray-600'
        }`}>
          📁 文件
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        className={`flex-1 py-3 items-center ${
          activeTab === 'chat' ? 'border-b-2 border-blue-500' : ''
        }`}
        onPress={() => setActiveTab('chat')}
        disabled={!aiAgent}
      >
        <Text
          className={`text-base ${
            activeTab === 'chat'
              ? 'text-blue-500 font-medium'
              : !aiAgent
                ? 'text-gray-300'
                : 'text-gray-600'
          }`}
        >
          🤖 AI助手
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />

      {/* 头部状态栏 */}
      <View className="bg-white p-4 border-b border-gray-200">
        <Text className="text-xl font-bold text-gray-800 mb-3">远程编码助手</Text>
        {renderConnectionStatus()}
        {renderAIStatus()}
      </View>

      {/* 标签栏 */}
      {renderTabBar()}

      {/* 主内容区域 */}
      <View className="flex-1">
        {activeTab === 'files' && connectionStatus === ConnectionStatus.CONNECTED && (
          <FileSystemBrowser
            sshService={sshService}
            onFileSelect={handleFileSelect}
            onDirectoryChange={setCurrentPath}
            currentPath={currentPath}
          />
        )}

        {activeTab === 'chat' && aiAgent && (
          <AIAssistantChat
            agent={aiAgent}
            codeContext={codeSelection || undefined}
            filePath={selectedFilePath || undefined}
            onClearContext={handleClearCodeContext}
          />
        )}

        {connectionStatus !== ConnectionStatus.CONNECTED && (
          <View className="flex-1 justify-center items-center">
            <Text className="text-base text-gray-600">请先建立SSH连接</Text>
          </View>
        )}
      </View>

      {/* SSH连接模态框 */}
      <SSHConnectionModal
        visible={showConnectionModal}
        onConnect={handleConnect}
        onCancel={() => setShowConnectionModal(false)}
      />

      {/* 代码查看器模态框 */}
      <Modal
        visible={showCodeViewer}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        {selectedFile && (
          <CodeViewer
            sshService={sshService}
            file={selectedFile}
            onCodeSelect={handleCodeSelect}
            onClose={() => setShowCodeViewer(false)}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
};


