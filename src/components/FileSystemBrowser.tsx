import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { FileSystemItem } from '../types';
import { SSHService } from '../services/SSHService';
import { getFileIcon, formatFileSize } from '../utils/fileUtils';

interface FileSystemBrowserProps {
  sshService: SSHService;
  onFileSelect: (file: FileSystemItem) => void;
  onDirectoryChange: (path: string) => void;
  currentPath: string;
}

export const FileSystemBrowser: React.FC<FileSystemBrowserProps> = ({
  sshService,
  onFileSelect,
  onDirectoryChange,
  currentPath,
}) => {
  const [items, setItems] = useState<FileSystemItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDirectory(currentPath);
  }, [currentPath]);

  const loadDirectory = async (path: string) => {
    if (!sshService.isConnected()) {
      setError('SSH连接未建立');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const directoryItems = await sshService.listDirectory(path);
      setItems(directoryItems);
    } catch (err) {
      setError(`加载目录失败: ${err}`);
      Alert.alert('错误', `无法加载目录: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  const handleItemPress = (item: FileSystemItem) => {
    if (item.type === 'directory') {
      onDirectoryChange(item.path);
    } else {
      onFileSelect(item);
    }
  };

  const navigateUp = () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
    onDirectoryChange(parentPath);
  };

  const getItemIcon = (item: FileSystemItem) => {
    return getFileIcon(item.name, item.type === 'directory');
  };

  const renderItem = ({ item }: { item: FileSystemItem }) => (
    <TouchableOpacity
      className="bg-white mx-3 my-0.5 rounded-md shadow-sm"
      onPress={() => handleItemPress(item)}
    >
      <View className="flex-row items-center p-3">
        <Text className="text-2xl mr-3">{getItemIcon(item)}</Text>
        <View className="flex-1">
          <Text className="text-base font-medium text-gray-800 mb-1">{item.name}</Text>
          <View className="flex-row justify-between">
            <Text className="text-xs text-gray-600">{formatFileSize(item.size)}</Text>
            <Text className="text-xs text-gray-600 font-mono">{item.permissions}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#007AFF" />
        <Text className="mt-3 text-base text-gray-600">加载中...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-100">
      {/* 路径导航 */}
      <View className="bg-white p-3 border-b border-gray-200">
        <TouchableOpacity
          className="bg-blue-500 px-3 py-1.5 rounded-md self-start mb-2"
          onPress={navigateUp}
        >
          <Text className="text-white text-sm font-medium">⬆️ 上级目录</Text>
        </TouchableOpacity>
        <Text className="text-sm text-gray-600 font-mono">{currentPath}</Text>
      </View>

      {/* 错误提示 */}
      {error && (
        <View className="bg-red-50 p-3 m-3 rounded-md border-l-4 border-red-500">
          <Text className="text-red-800 text-sm">{error}</Text>
        </View>
      )}

      {/* 文件列表 */}
      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => item.path}
        className="flex-1"
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};


