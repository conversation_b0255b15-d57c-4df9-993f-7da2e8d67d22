import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { AIMessage, AIAssistantStatus, CodeSelection } from '../types';
import { AIAgent } from '../types';

interface AIAssistantChatProps {
  agent: AIAgent;
  codeContext?: CodeSelection;
  filePath?: string;
  onClearContext: () => void;
}

export const AIAssistantChat: React.FC<AIAssistantChatProps> = ({
  agent,
  codeContext,
  filePath,
  onClearContext,
}) => {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [status, setStatus] = useState<AIAssistantStatus>(AIAssistantStatus.IDLE);
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    // 监听Agent状态变化
    agent.onStatusChange((newStatus) => {
      setStatus(newStatus);
      if (newStatus === AIAssistantStatus.PROCESSING) {
        setIsTyping(true);
      } else {
        setIsTyping(false);
      }
    });

    // 监听Agent消息
    agent.onMessage((message) => {
      setMessages(prev => [...prev, message]);
      // 自动滚动到底部
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    });

    // 获取初始状态
    setStatus(agent.getStatus());
  }, [agent]);

  const sendMessage = async () => {
    if (!inputText.trim() || status !== AIAssistantStatus.READY) {
      return;
    }

    const messageText = inputText.trim();
    setInputText('');

    try {
      await agent.sendMessage(messageText, codeContext, filePath);
    } catch (error) {
      Alert.alert('错误', `发送消息失败: ${error}`);
    }
  };

  const getStatusText = () => {
    switch (status) {
      case AIAssistantStatus.IDLE:
        return '未启动';
      case AIAssistantStatus.STARTING:
        return '启动中...';
      case AIAssistantStatus.READY:
        return '就绪';
      case AIAssistantStatus.PROCESSING:
        return '处理中...';
      case AIAssistantStatus.ERROR:
        return '错误';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case AIAssistantStatus.READY:
        return '#4CAF50';
      case AIAssistantStatus.PROCESSING:
        return '#FF9800';
      case AIAssistantStatus.ERROR:
        return '#F44336';
      default:
        return '#9E9E9E';
    }
  };

  const renderMessage = ({ item }: { item: AIMessage }) => (
    <View
      className={`my-1 ${
        item.type === 'user' ? 'items-end' : 'items-start'
      }`}
    >
      <View
        className={`max-w-4/5 p-3 rounded-2xl ${
          item.type === 'user'
            ? 'bg-blue-500'
            : 'bg-white border border-gray-200'
        }`}
      >
        {/* 代码上下文显示 */}
        {item.codeContext && item.filePath && (
          <View className="mb-2 p-2 bg-black bg-opacity-5 rounded-md">
            <Text className="text-xs text-gray-600 mb-1 font-medium">
              📄 {item.filePath} (第{item.codeContext.startLine}-{item.codeContext.endLine}行)
            </Text>
            <View className="bg-gray-100 p-2 rounded border-l-4 border-blue-500">
              <Text className="text-xs font-mono text-gray-800">
                {item.codeContext.selectedText}
              </Text>
            </View>
          </View>
        )}

        <Text
          className={`text-base leading-6 ${
            item.type === 'user' ? 'text-white' : 'text-gray-800'
          }`}
        >
          {item.content}
        </Text>

        <Text
          className={`text-xs mt-1 ${
            item.type === 'user'
              ? 'text-white text-opacity-70'
              : 'text-gray-500'
          }`}
        >
          {item.timestamp.toLocaleTimeString()}
        </Text>
      </View>
    </View>
  );

  const renderTypingIndicator = () => {
    if (!isTyping) return null;

    return (
      <View className="my-1 items-start">
        <View className="max-w-4/5 p-3 rounded-2xl bg-white border border-gray-200">
          <View className="flex-row items-center">
            <ActivityIndicator size="small" color="#666" />
            <Text className="ml-2 text-sm text-gray-600 italic">AI助手正在思考...</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-gray-100"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* 状态栏 */}
      <View className="bg-white p-3 border-b border-gray-200">
        <View className="flex-row items-center mb-2">
          <View
            className="w-2 h-2 rounded-full mr-2"
            style={{ backgroundColor: getStatusColor() }}
          />
          <Text className="text-sm text-gray-600 font-medium">{getStatusText()}</Text>
        </View>

        {/* 代码上下文显示 */}
        {codeContext && filePath && (
          <View className="flex-row items-center bg-blue-50 p-2 rounded-md border-l-4 border-blue-500">
            <Text className="flex-1 text-xs text-blue-800">
              📄 {filePath.split('/').pop()} (第{codeContext.startLine}-{codeContext.endLine}行)
            </Text>
            <TouchableOpacity className="p-1 ml-2" onPress={onClearContext}>
              <Text className="text-blue-800 text-base font-bold">✕</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* 消息列表 */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        className="flex-1 px-3"
        showsVerticalScrollIndicator={false}
        ListFooterComponent={renderTypingIndicator}
      />

      {/* 输入区域 */}
      <View className="flex-row items-end bg-white px-3 py-2 border-t border-gray-200">
        <TextInput
          className="flex-1 border border-gray-200 rounded-2xl px-4 py-2 max-h-25 text-base text-gray-800"
          value={inputText}
          onChangeText={setInputText}
          placeholder="输入消息..."
          placeholderTextColor="#999"
          multiline
          maxLength={1000}
          editable={status === AIAssistantStatus.READY}
        />
        <TouchableOpacity
          className={`px-4 py-2.5 rounded-2xl ml-2 ${
            status !== AIAssistantStatus.READY || !inputText.trim()
              ? 'bg-gray-300'
              : 'bg-blue-500'
          }`}
          onPress={sendMessage}
          disabled={status !== AIAssistantStatus.READY || !inputText.trim()}
        >
          <Text className="text-white text-base font-medium">发送</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};


