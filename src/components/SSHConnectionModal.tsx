import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SSHConfig } from '../types';

interface SSHConnectionModalProps {
  visible: boolean;
  onConnect: (config: SSHConfig) => void;
  onCancel: () => void;
}

export const SSHConnectionModal: React.FC<SSHConnectionModalProps> = ({
  visible,
  onConnect,
  onCancel,
}) => {
  const [host, setHost] = useState('');
  const [port, setPort] = useState('22');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [usePrivateKey, setUsePrivateKey] = useState(false);
  const [privateKey, setPrivateKey] = useState('');
  const [passphrase, setPassphrase] = useState('');

  const handleConnect = () => {
    if (!host.trim() || !username.trim()) {
      Alert.alert('错误', '请填写主机地址和用户名');
      return;
    }

    if (!usePrivateKey && !password.trim()) {
      Alert.alert('错误', '请填写密码或选择使用私钥');
      return;
    }

    if (usePrivateKey && !privateKey.trim()) {
      Alert.alert('错误', '请填写私钥内容');
      return;
    }

    const config: SSHConfig = {
      host: host.trim(),
      port: parseInt(port) || 22,
      username: username.trim(),
      password: usePrivateKey ? undefined : password,
      privateKey: usePrivateKey ? privateKey.trim() : undefined,
      passphrase: passphrase.trim() || undefined,
    };

    onConnect(config);
  };

  const resetForm = () => {
    setHost('');
    setPort('22');
    setUsername('');
    setPassword('');
    setUsePrivateKey(false);
    setPrivateKey('');
    setPassphrase('');
  };

  const handleCancel = () => {
    resetForm();
    onCancel();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleCancel}
    >
      <KeyboardAvoidingView
        className="flex-1 bg-gray-100"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View className="flex-row items-center justify-between bg-white px-4 py-3 border-b border-gray-200">
          <TouchableOpacity className="p-2" onPress={handleCancel}>
            <Text className="text-blue-500 text-base">取消</Text>
          </TouchableOpacity>
          <Text className="text-lg font-bold text-gray-800">SSH连接</Text>
          <TouchableOpacity
            className="bg-blue-500 px-4 py-2 rounded-md"
            onPress={handleConnect}
          >
            <Text className="text-white text-base font-medium">连接</Text>
          </TouchableOpacity>
        </View>

        <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
          {/* 主机地址 */}
          <View className="mb-5">
            <Text className="text-base font-medium text-gray-800 mb-2">主机地址 *</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-3 py-3 text-base bg-white text-gray-800"
              value={host}
              onChangeText={setHost}
              placeholder="例如: *************"
              placeholderTextColor="#999"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* 端口 */}
          <View className="mb-5">
            <Text className="text-base font-medium text-gray-800 mb-2">端口</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-3 py-3 text-base bg-white text-gray-800"
              value={port}
              onChangeText={setPort}
              placeholder="22"
              placeholderTextColor="#999"
              keyboardType="numeric"
            />
          </View>

          {/* 用户名 */}
          <View className="mb-5">
            <Text className="text-base font-medium text-gray-800 mb-2">用户名 *</Text>
            <TextInput
              className="border border-gray-300 rounded-lg px-3 py-3 text-base bg-white text-gray-800"
              value={username}
              onChangeText={setUsername}
              placeholder="例如: root"
              placeholderTextColor="#999"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* 认证方式选择 */}
          <View className="flex-row mb-5 bg-white rounded-lg p-1">
            <TouchableOpacity
              className={`flex-1 py-3 items-center rounded-md ${
                !usePrivateKey ? 'bg-blue-500' : ''
              }`}
              onPress={() => setUsePrivateKey(false)}
            >
              <Text className={`text-base ${
                !usePrivateKey ? 'text-white font-medium' : 'text-gray-600'
              }`}>
                密码认证
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-3 items-center rounded-md ${
                usePrivateKey ? 'bg-blue-500' : ''
              }`}
              onPress={() => setUsePrivateKey(true)}
            >
              <Text className={`text-base ${
                usePrivateKey ? 'text-white font-medium' : 'text-gray-600'
              }`}>
                私钥认证
              </Text>
            </TouchableOpacity>
          </View>

          {/* 密码认证 */}
          {!usePrivateKey && (
            <View className="mb-5">
              <Text className="text-base font-medium text-gray-800 mb-2">密码 *</Text>
              <TextInput
                className="border border-gray-300 rounded-lg px-3 py-3 text-base bg-white text-gray-800"
                value={password}
                onChangeText={setPassword}
                placeholder="请输入密码"
                placeholderTextColor="#999"
                secureTextEntry
              />
            </View>
          )}

          {/* 私钥认证 */}
          {usePrivateKey && (
            <>
              <View className="mb-5">
                <Text className="text-base font-medium text-gray-800 mb-2">私钥内容 *</Text>
                <TextInput
                  className="border border-gray-300 rounded-lg px-3 py-3 text-base bg-white text-gray-800 h-30"
                  style={{ textAlignVertical: 'top' }}
                  value={privateKey}
                  onChangeText={setPrivateKey}
                  placeholder="请粘贴私钥内容（PEM格式）"
                  placeholderTextColor="#999"
                  multiline
                  numberOfLines={6}
                />
              </View>

              <View className="mb-5">
                <Text className="text-base font-medium text-gray-800 mb-2">私钥密码（可选）</Text>
                <TextInput
                  className="border border-gray-300 rounded-lg px-3 py-3 text-base bg-white text-gray-800"
                  value={passphrase}
                  onChangeText={setPassphrase}
                  placeholder="如果私钥有密码请输入"
                  placeholderTextColor="#999"
                  secureTextEntry
                />
              </View>
            </>
          )}

          {/* 连接提示 */}
          <View className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-500 mt-5">
            <Text className="text-sm text-blue-800 leading-5">
              💡 提示：确保目标服务器已安装Claude Code，并且您有足够的权限访问项目目录。
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};


